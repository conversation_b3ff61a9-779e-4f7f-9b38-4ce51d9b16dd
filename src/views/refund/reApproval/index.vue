<template>
  <PageList
    :indexPageConfig="pageConfig.indexPageConfig"
    :addPageConfig="pageConfig.addPageConfig"
  />
</template>

<script>
// 客户主数据
import PageList from "@/components/PageList/index.vue";
import { pageConfig } from "./config";

export default {
  name: "ReShipmentHis",
  components: {
    PageList,
  },
  dicts: [],
  dictsKey: ["VKORG",'customer_type','release_status'],
  data() {
    return {
      pageConfig,
    };
  },
};
</script>
