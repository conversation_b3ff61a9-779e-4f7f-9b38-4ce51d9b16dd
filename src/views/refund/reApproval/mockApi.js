// 模拟数据
const mockData = [
  {
    reOrderId: '1',
    torderSqno: 'XT20250001',
    name1: '北京玉柴专卖店',
    customerType: '1',
    returnReason: '1',
    releaseStatus: '0',
    vkorg: '1000',
    kunnr: '100001',
    createdDate: '2025-01-15',
    createdBy: 'admin',
    documentStatus: '1'
  },
  {
    reOrderId: '2',
    torderSqno: 'XT20250002',
    name1: '上海玉柴服务站',
    customerType: '2',
    returnReason: '2',
    releaseStatus: '1',
    vkorg: '1000',
    kunnr: '100002',
    createdDate: '2025-01-16',
    createdBy: 'admin',
    documentStatus: '2'
  },
  {
    reOrderId: '3',
    torderSqno: 'XT20250003',
    name1: '广州玉柴专卖店',
    customerType: '1',
    returnReason: '3',
    releaseStatus: '0',
    vkorg: '2000',
    kunnr: '200001',
    createdDate: '2025-01-17',
    createdBy: 'user1',
    documentStatus: '1'
  },
  {
    reOrderId: '4',
    torderSqno: 'XT20250004',
    name1: '深圳玉柴服务站',
    customerType: '2',
    returnReason: '4',
    releaseStatus: '1',
    vkorg: '2000',
    kunnr: '200002',
    createdDate: '2025-01-18',
    createdBy: 'user2',
    documentStatus: '2'
  },
  {
    reOrderId: '5',
    torderSqno: 'XT20250005',
    name1: '成都玉柴专卖店',
    customerType: '1',
    returnReason: '1',
    releaseStatus: '0',
    vkorg: '3000',
    kunnr: '300001',
    createdDate: '2025-01-19',
    createdBy: 'admin',
    documentStatus: '1'
  },
  {
    reOrderId: '6',
    torderSqno: 'XT20250006',
    name1: '武汉玉柴服务站',
    customerType: '2',
    returnReason: '2',
    releaseStatus: '1',
    vkorg: '1000',
    kunnr: '100003',
    createdDate: '2025-01-20',
    createdBy: 'admin',
    documentStatus: '2'
  },
  {
    reOrderId: '7',
    torderSqno: 'XT20250007',
    name1: '西安玉柴专卖店',
    customerType: '1',
    returnReason: '4',
    releaseStatus: '0',
    vkorg: '3000',
    kunnr: '300002',
    createdDate: '2025-01-21',
    createdBy: 'user3',
    documentStatus: '1'
  },
  {
    reOrderId: '8',
    torderSqno: 'XT20250008',
    name1: '杭州玉柴服务站',
    customerType: '2',
    returnReason: '3',
    releaseStatus: '1',
    vkorg: '2000',
    kunnr: '200003',
    createdDate: '2025-01-22',
    createdBy: 'admin',
    documentStatus: '2'
  }
];

// 查询期初历史退货列表
export function listReOrderHis(query) {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockData];
      
      // 模拟搜索过滤
      if (query.torderSqno) {
        filteredData = filteredData.filter(item => 
          item.torderSqno.includes(query.torderSqno)
        );
      }
      if (query.kunnr) {
        filteredData = filteredData.filter(item => 
          item.kunnr.includes(query.kunnr) || item.name1.includes(query.kunnr)
        );
      }
      if (query.customerType) {
        filteredData = filteredData.filter(item => 
          item.customerType === query.customerType
        );
      }
      if (query.releaseStatus) {
        filteredData = filteredData.filter(item => 
          item.releaseStatus === query.releaseStatus
        );
      }
      
      // 模拟分页
      const pageNum = query.pageNum || 1;
      const pageSize = query.pageSize || 10;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const rows = filteredData.slice(start, end);
      
      resolve({
        code: 200,
        msg: '查询成功',
        rows: rows,
        total: filteredData.length
      });
    }, 300);
  });
}

// 查询期初历史退货详细
export function getReOrderHis(reOrderId) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.reOrderId === reOrderId);
      if (item) {
        resolve({
          code: 200,
          msg: '查询成功',
          data: item
        });
      } else {
        resolve({
          code: 404,
          msg: '数据不存在'
        });
      }
    }, 200);
  });
}

// 新增期初历史退货
export function addReOrderHis(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = (mockData.length + 1).toString();
      const newItem = {
        ...data,
        reOrderId: newId,
        torderSqno: `XT2025${String(mockData.length + 1).padStart(4, '0')}`,
        createdDate: new Date().toISOString().split('T')[0],
        createdBy: 'admin',
        documentStatus: '1',
        releaseStatus: '0'
      };
      mockData.push(newItem);
      
      resolve({
        code: 200,
        msg: '新增成功',
        data: newItem
      });
    }, 300);
  });
}

// 修改期初历史退货
export function updateReOrderHis(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.findIndex(item => item.reOrderId === data.reOrderId);
      if (index !== -1) {
        mockData[index] = { ...mockData[index], ...data };
        resolve({
          code: 200,
          msg: '修改成功',
          data: mockData[index]
        });
      } else {
        resolve({
          code: 404,
          msg: '数据不存在'
        });
      }
    }, 300);
  });
}

// 删除期初历史退货
export function delReOrderHis(reOrderId) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.findIndex(item => item.reOrderId === reOrderId);
      if (index !== -1) {
        mockData.splice(index, 1);
        resolve({
          code: 200,
          msg: '删除成功'
        });
      } else {
        resolve({
          code: 404,
          msg: '数据不存在'
        });
      }
    }, 300);
  });
}
