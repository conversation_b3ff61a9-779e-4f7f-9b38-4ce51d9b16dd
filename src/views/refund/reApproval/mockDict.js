// 模拟字典数据
export const mockDictData = {
  // 客户类型
  customer_type: [
    { dictValue: '1', dictLabel: '专卖店', value: '1', label: '专卖店' },
    { dictValue: '2', dictLabel: '服务站', value: '2', label: '服务站' }
  ],
  
  // 审批状态
  release_status: [
    { dictValue: '0', dictLabel: '未审批', value: '0', label: '未审批' },
    { dictValue: '1', dictLabel: '已审批', value: '1', label: '已审批' }
  ],
  
  // 退货原因
  return_reason: [
    { dictValue: '1', dictLabel: '新品投放退货', value: '1', label: '新品投放退货' },
    { dictValue: '2', dictLabel: '农机投放退货', value: '2', label: '农机投放退货' },
    { dictValue: '3', dictLabel: 'PJ退货', value: '3', label: 'PJ退货' },
    { dictValue: '4', dictLabel: '走账退货', value: '4', label: '走账退货' }
  ],
  
  // 单据状态
  document_status: [
    { dictValue: '1', dictLabel: '未提交', value: '1', label: '未提交' },
    { dictValue: '2', dictLabel: '已提交', value: '2', label: '已提交' },
    { dictValue: '3', dictLabel: '已关闭', value: '3', label: '已关闭' }
  ]
};

// 获取字典数据的函数
export function getMockDictData(dictType) {
  return mockDictData[dictType] || [];
}
